services:
  project:
    image: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-classic-wrapper-backend:${TAG}
    user: "node:node"
    read_only: true
    tmpfs: /tmp
    security_opt:
      - no-new-privileges:true
    build:
      context: .
      target: builder
      args:
        ARTIFACTORY_TOKEN_PLATFORM: ${ARTIFACTORY_TOKEN_PLATFORM}
    environment:
      NODE_OPTIONS: --max-old-space-size=2048
      NODE_ENV: develop
      KAFKA_HOST: testkafka:9092
      LOG_LEVEL: error
      JEST_JUNIT_OUTPUT_DIR: ./reports/junit/
      DATABASE_URL: ***************************************/postgres
      DATABASE_READ_URL: ***************************************/postgres
      PUBLISH_S3_BUCKET: ${PUBLISH_S3_BUCKET:-}
      PUBLISH_S3_PREFIX: ${PUBLISH_S3_PREFIX:-}
      PUBLISH_S3_REGION: ${PUBLISH_S3_REGION:-}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:-}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:-}
      KEYCLOAK_BASE_URL: http://keycloak:8080/auth
    ports:
      - '3000:80'
      - '8080:8080'
    volumes:
      - './src:/srv/src'
      - './test:/srv/test'
      - './config:/srv/config'
      - './swagger:/srv/swagger'
      - './coverage:/srv/coverage'
      - './reports:/srv/reports'
      - './migration:/srv/migration'
      # - './node_modules:/srv/node_modules' # can be useful to troubleshoot dependencies
    links:
     - postgres
     - keycloak
    depends_on:
      - keycloak
      - testkafka
      - postgres

  postgres:
      image: postgres:15.5
      read_only: true
      # For read-only filesystem, need to create a volume/tmpfs for PostgreSQL to run its much
      # needed configuration. The read-only flag does not make volumes and tmpfs read-only.
      tmpfs:
        - /tmp
        - /run
        - /run/postgresql
      security_opt:
        - no-new-privileges:true
      environment:
        POSTGRES_PASSWORD: dev
      ports:
        - '15432:5432'

  keycloak:
    # Ensure this keycloak image remains in sync with keycloak in other docker-compose files
    image: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-test-keycloak:086ae1c0b8684fa3aa4d80b4bdb79117ddb1df3b
    ports:
      - '18080:8080'
    security_opt:
      - no-new-privileges:true

  testkafka:
    image: artifacts.ol.epicgames.net/supawe-kws-docker/superawesomeltd/freekws-test-kafka:05042e3d386a8000d75626d1ce941961c6f39e0e
    hostname: testkafka
    security_opt:
      - no-new-privileges:true
