import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsISO8601,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Matches,
  MinLength,
  ValidateNested,
} from 'class-validator';

export class UserAppActivationDTO {
  @ApiProperty({
    description: 'Client ID of the application.',
    example: 'frog-fractions',
  })
  @IsString()
  @IsNotEmpty()
  appName: string;

  @ApiProperty({
    description:
      '[Deprecated] This value is unused and if passed, will be ignored.\nDisplay name of the user within the application. Unique across users and applications.',
    example: 'PlayerOne',
    nullable: true,
    deprecated: true,
  })
  @IsString()
  @IsOptional()
  displayName?: string;

  @ApiProperty({
    description: 'Permissions to request, if desired, in the form of an array of permission identifiers.',
    example: ['read_profile', 'send_messages'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => value || [])
  permissions?: string[];

  @ApiProperty({
    description:
      '[Deprecated] This value is unused and if passed, will be ignored.\nDate of birth of the child in ISO 8601 format (YYYY-MM-DD)',
    example: '2010-01-01',
    deprecated: true,
  })
  @IsOptional()
  @Transform(() => {
    return;
  })
  dateOfBirth?: undefined;

  @ApiProperty({
    description: "This value is unused and if passed, will be ignored.\nParent's email address",
    example: '<EMAIL>',
    deprecated: true,
  })
  @IsEmail()
  @IsOptional()
  parentEmail?: string;
}

export class UserRegisterDTO {
  @ApiProperty({
    description: 'Username of the user. Must start with a letter and be at least 3 characters long.',
    example: 'cooluser123',
    required: false,
  })
  @IsString()
  @Matches(/^[A-z][\w-]{2,}$/, {
    message:
      'Username must start with a letter and contain at least 3 characters. Only letters, numbers, underscores, and hyphens are allowed.',
  })
  username: string;

  @ApiProperty({
    description: '[DEPRECATED] Email address of the user.',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase())
  email?: string;

  @ApiProperty({
    description: 'Password for the user account. Must be at least 5 characters long.',
    example: 'Password123!',
    required: false,
  })
  @IsString()
  @MinLength(5, { message: 'Password must be at least 5 characters long' })
  password: string;

  @ApiProperty({
    description: 'Date of birth in ISO 8601 format (YYYY-MM-DD)',
    example: '2000-01-01',
    required: false,
  })
  @IsISO8601({ strict: true }, { message: 'Date of birth must be in ISO 8601 format (YYYY-MM-DD)' })
  dateOfBirth: string;

  @ApiProperty({
    description: 'Country code in ISO 3166-1 alpha-2 format',
    example: 'US',
    required: false,
    default: 'ZZ',
  })
  @IsString()
  @IsOptional()
  country?: string = 'ZZ';

  @ApiProperty({
    description: 'Parent email address for child accounts',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail({}, { message: 'Invalid parent email format' })
  @Transform(({ value }) => value?.toLowerCase())
  @IsOptional()
  parentEmail?: string;

  @ApiProperty({
    description: 'ID of the application where the user is registering from',
    example: 123,
    required: false,
  })
  @IsNumber()
  originAppId: number;

  @ApiProperty({
    description: 'Authentication or verification token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: false,
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'Preferred language code',
    example: 'en',
    required: false,
    default: 'en',
  })
  @IsString()
  @IsOptional()
  language?: string = 'en';
}

export class UserDeletionDTO {
  @ApiProperty({
    description: '[Deprecated] If provided, will throw a bad request.\nUser password for account deletion verification',
    example: 'Password123!',
    required: false,
  })
  @IsString()
  @IsOptional()
  password?: string;
}

class UsernameCheckDetailsDTO {
  @ApiProperty({
    description: 'Indicates if the username passes moderation checks',
    example: true,
  })
  @IsBoolean()
  isValid: boolean;

  @ApiProperty({
    description: 'Indicates if the username is not already taken',
    example: true,
  })
  @IsBoolean()
  isAvailable: boolean;
}

export class UsernameCheckResponseDTO {
  @ApiProperty({
    description: 'The username that was checked',
    example: 'cooluser123',
  })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({
    description: 'Indicates if the username is available for use (passes both availability and moderation checks)',
    example: true,
  })
  @IsBoolean()
  available: boolean;

  @ApiProperty({
    description: 'Detailed information about the username check',
    type: UsernameCheckDetailsDTO,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => UsernameCheckDetailsDTO)
  details: UsernameCheckDetailsDTO;
}

export class UserSearchResponseDTO {
  @ApiProperty({
    description: 'The username of the user',
    example: 'bob1',
  })
  username: string;

  @ApiProperty({
    description: 'The parent email associated with the user',
    example: '<EMAIL>',
  })
  parentEmail: string;
}
